import React, { useState, useEffect } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { useAuth } from './contexts/AuthContext'

function Login() {
    const [username, setUsername] = useState('')
    const [password, setPassword] = useState('')
    const [error, setError] = useState('')
    const [loading, setLoading] = useState(false)
    const navigate = useNavigate()
    const { login, isAuthenticated, loading: authLoading } = useAuth()

    // Redirect if already authenticated
    useEffect(() => {
        console.log('Login useEffect:', { isAuthenticated, authLoading })
        if (isAuthenticated && !authLoading) {
            console.log('Redirecting to / from useEffect')
            navigate('/')
        }
    }, [isAuthenticated, authLoading, navigate])

    const handleSubmit = async (e) => {
        e.preventDefault()
        setError('')
        setLoading(true)

        try {
            const result = await login(username, password)
            console.log('Login result:', result)
            if (result.success) {
                console.log('Login successful, navigating to /')
                navigate('/')
            } else {
                setError(result.message || 'Invalid username or password')
            }
        } catch (error) {
            console.error('Login error:', error)
            setError('Login failed. Please try again.')
        }

        setLoading(false)
    }



    // Show loading while checking authentication
    if (authLoading) {
        return (
            <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
                <div className='bg-white p-4 rounded text-center'>
                    <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                    </div>
                    <p className='mt-2'>Checking authentication...</p>
                </div>
            </div>
        )
    }

    // Redirect immediately if already authenticated (no welcome screen)
    if (isAuthenticated) {
        console.log('Already authenticated, redirecting to /')
        navigate('/', { replace: true })
        return null
    }

    return (
        <div className='min-vh-100 bg-primary d-flex justify-content-center align-items-center py-4'>
            <div className='container-fluid'>
                <div className='row justify-content-center'>
                    <div className='col-lg-6 col-md-8 col-sm-10'>
                        <div className='bg-white p-4 rounded shadow'>
                <form onSubmit={handleSubmit}>
                    <h2 className='mb-4 text-center'>Admin Login</h2>
                    
                    {error && (
                        <div className='alert alert-danger' role='alert'>
                            {error}
                        </div>
                    )}

                    <div className='mb-3'>
                        <label className='form-label'>Username</label>
                        <input 
                            type="text" 
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder='Enter username' 
                            className='form-control'
                            required
                        />
                    </div>

                    <div className='mb-3'>
                        <label className='form-label'>Password</label>
                        <input 
                            type="password" 
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder='Enter password' 
                            className='form-control'
                            required
                        />
                    </div>

                    <button 
                        type="submit" 
                        className='btn btn-success w-100 mb-3'
                        disabled={loading}
                    >
                        {loading ? 'Logging in...' : 'Login'}
                    </button>

                    <div className='text-center'>
                        <div className='alert alert-info'>
                            <strong>Demo Credentials:</strong><br/>
                            Username: <code>admin</code><br/>
                            Password: <code>admin123</code>
                        </div>
                        <p>
                            <Link to='/'>View Users (Public)</Link>
                        </p>
                            </div>
                        </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Login
