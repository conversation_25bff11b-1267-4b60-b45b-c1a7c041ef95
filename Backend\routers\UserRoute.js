import express from 'express'
import UserModel from '../models/User.js'
import multer from 'multer'
import path from 'path'
import fs from 'fs'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcrypt'

// Create uploads directory if it doesn't exist
const uploadsDir = 'uploads'
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true })
}

const storage=multer.diskStorage({
    destination:(req,file,cb)=>{
        cb(null,'uploads/')
    },
    filename:(req,file,cb)=>{
        cb(null,Date.now()+path.extname(file.originalname))
    }
})

const upload = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true)
        } else {
            cb(new Error('Only image files are allowed!'), false)
        }
    },
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    }
})

const router=express()

// JWT Secret (in production, use environment variable)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key-here'

// Admin credentials (in production, store in database with hashed password)
const ADMIN_CREDENTIALS = {
    username: 'admin',
    password: 'admin123' // In production, this should be hashed
}

// Middleware to verify JWT token
const verifyToken = (req, res, next) => {
    const token = req.headers.authorization?.split(' ')[1] // Bearer token

    if (!token) {
        return res.status(401).json({ message: 'Access denied. No token provided.' })
    }

    try {
        const decoded = jwt.verify(token, JWT_SECRET)
        req.user = decoded
        next()
    } catch (error) {
        res.status(400).json({ message: 'Invalid token.' })
    }
}

// Login route
router.post('/login', (req, res) => {
    const { username, password } = req.body

    // Validate credentials
    if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
        // Generate JWT token with session ID
        const sessionId = Date.now().toString() + Math.random().toString(36).substr(2, 9)
        const token = jwt.sign(
            {
                username: username,
                sessionId: sessionId,
                loginTime: new Date().toISOString()
            },
            JWT_SECRET,
            { expiresIn: '24h' }
        )

        res.json({
            success: true,
            token: token,
            user: {
                username: username,
                sessionId: sessionId
            }
        })
    } else {
        res.status(401).json({ success: false, message: 'Invalid credentials' })
    }
})

// Logout route
router.post('/logout', verifyToken, (req, res) => {
    // In a real application, you might want to blacklist the token
    res.json({ success: true, message: 'Logged out successfully' })
})

// Verify token route
router.get('/verify-token', verifyToken, (req, res) => {
    res.json({
        success: true,
        user: {
            username: req.user.username,
            sessionId: req.user.sessionId
        }
    })
})


router.post('/create', upload.single('image'), (req,res)=>{
    const userData = {
        name: req.body.name,
        email: req.body.email,
        password:req.body.password,
        age: req.body.age,
        gender:req.body.gender,
        technicalSkills:req.body.technicalSkills,
        image: req.file ? req.file.filename : ''
    }

    UserModel.create(userData).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.get('/users',(req,res)=>{
    UserModel.find({}).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.get('/user/:id',(req,res)=>{
    UserModel.findById(req.params.id).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.put('/update/:id', verifyToken, upload.single('image'), (req,res)=>{
    const updateData = {
        name: req.body.name,
        email: req.body.email,
        password:req.body.password,
        age: req.body.age,
        gender:req.body.gender,
        technicalSkills:req.body.technicalSkills,

    }

    // Only update image if a new one is uploaded
    if (req.file) {
        updateData.image = req.file.filename
    }

    UserModel.findByIdAndUpdate(req.params.id, updateData, {new:true}).then((data)=>{
        res.json(data)
    })
    .catch((Err)=>{
        res.status(400).json(Err)
    })
})

router.delete('/delete/:id', verifyToken, (req,res)=>{
    UserModel.findByIdAndDelete(req.params.id).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.json(err)
    })
})

export default router