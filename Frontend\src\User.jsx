import React, {useEffect, useState} from 'react'
import { Link, useNavigate } from 'react-router-dom'
import axios from 'axios'
import { useAuth } from './contexts/AuthContext'

function User() {
    // Initialize with empty array, data will be loaded from API
    const [users, setUsers] = useState([])
    const navigate = useNavigate()
    const { user, logout } = useAuth()
    
    useEffect(() => {
      axios.get('http://localhost:4000/api/users').then((data)=>{
        setUsers(data.data)
      })
      .catch((err)=>{
        console.log(err)
      })
    }, [])

    const DeleteHandler = async (id) => {
        try {
            await axios.delete(`http://localhost:4000/api/delete/${id}`)
            setUsers(users.filter((user) => user._id !== id))
        } catch (error) {
            if (error.response?.status === 401) {
                alert('Session expired. Please login again.')
                await logout()
                navigate('/login')
            } else {
                alert('Failed to delete user. Please try again.')
            }
        }
    }

    const handleLogout = async () => {
        await logout()
        navigate('/login')
    }
    
  return (
    <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
        <div className='bg-white w-70 rounded p-3'>
            <div className='d-flex justify-content-between align-items-center mb-3'>
                <div>
                    <Link to='/create' className='btn btn-success btn-sm me-2'>ADD + </Link>
                    {user && (
                        <span className='badge bg-info text-dark'>
                            Welcome, {user.username}! (Session: {user.sessionId?.slice(-4)})
                        </span>
                    )}
                </div>
                <div>
                    <button
                        onClick={handleLogout}
                        className='btn btn-outline-danger btn-sm'
                    >
                        Logout
                    </button>
                </div>
            </div>
            <table className='table'>
                <thead>
                   <tr>
                     <th>Image</th>
                     <th>Name</th>
                    <th>Email</th>
                    <th>Password</th>
                    <th>Age</th>
                    <th>Gender</th>
                    <th>Technical Skills</th>
                    <th>Action</th>
                   </tr>
                </thead>
                <tbody>
                    {
                        users.map((user, index)=>{
                            return <tr key={user._id || index}>
                                <td>
                                    {user.image ? (
                                        <img
                                            src={`http://localhost:4000/uploads/${user.image}`}
                                            alt={user.name}
                                            style={{width: '50px', height: '50px', 
                                                objectFit: 'cover', borderRadius: '50%'}}
                                        />
                                    ) : (
                                        <div
                                            style={{
                                                width: '50px',
                                                height: '50px',
                                                backgroundColor: '#ccc',
                                                borderRadius: '50%',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                fontSize: '12px'
                                            }}
                                        >
                                            No Image
                                        </div>
                                    )}
                                </td>
                                <td>{user.name}</td>
                                <td>{user.email}</td>
                                <td>{user.password}</td>
                                <td>{user.age}</td>
                                <td>{user.gender}</td>
                                <td>{user.technicalSkills}</td>
                                <td>
                                    <Link to={`/update/${user._id}`} className='btn btn-success me-2'>
                                        Edit
                                    </Link>
                                    <button
                                        className='btn btn-danger'
                                        onClick={() => DeleteHandler(user._id)}
                                    >
                                        Delete
                                    </button>
                                </td>
                            </tr>
                        })
                    }
                </tbody>
            </table>
        </div>
    </div>
  )
}

export default User
