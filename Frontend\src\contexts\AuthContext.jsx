import React, { createContext, useContext, useState, useEffect } from 'react'
import axios from 'axios'

const AuthContext = createContext()

export const useAuth = () => {
    const context = useContext(AuthContext)
    if (!context) {
        throw new Error('useAuth must be used within an AuthProvider')
    }
    return context
}

export const AuthProvider = ({ children }) => {
    const [isAuthenticated, setIsAuthenticated] = useState(false)
    const [user, setUser] = useState(null)
    const [loading, setLoading] = useState(true)
    const [token, setToken] = useState(sessionStorage.getItem('authToken'))

    // Set up axios interceptor for token
    useEffect(() => {
        if (token) {
            axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        } else {
            delete axios.defaults.headers.common['Authorization']
        }
    }, [token])

    // Check if user is authenticated on app load
    useEffect(() => {
        const checkAuth = async () => {
            const storedToken = sessionStorage.getItem('authToken')
            if (storedToken) {
                try {
                    const response = await axios.get('http://localhost:4000/api/verify-token')
                    if (response.data.success) {
                        setIsAuthenticated(true)
                        setUser(response.data.user)
                        setToken(storedToken)
                    } else {
                        // Token is invalid, remove it
                        sessionStorage.removeItem('authToken')
                        setToken(null)
                    }
                } catch (error) {
                    // Token is invalid, remove it
                    sessionStorage.removeItem('authToken')
                    setToken(null)
                }
            }
            setLoading(false)
        }

        checkAuth()
    }, [])

    const login = async (username, password) => {
        try {
            const response = await axios.post('http://localhost:4000/api/login', {
                username,
                password
            })

            if (response.data.success) {
                const { token, user } = response.data

                // Store token in sessionStorage (not localStorage for better security)
                sessionStorage.setItem('authToken', token)

                setToken(token)
                setUser(user)
                setIsAuthenticated(true)

                console.log('Login successful:', { user, token: token.substring(0, 20) + '...' })
                return { success: true }
            } else {
                return { success: false, message: response.data.message }
            }
        } catch (error) {
            return { 
                success: false, 
                message: error.response?.data?.message || 'Login failed' 
            }
        }
    }

    const logout = async () => {
        try {
            // Call logout endpoint if authenticated
            if (token) {
                await axios.post('http://localhost:4000/api/logout')
            }
        } catch (error) {
            console.error('Logout error:', error)
        } finally {
            // Clear local state regardless of API call success
            sessionStorage.removeItem('authToken')
            setToken(null)
            setUser(null)
            setIsAuthenticated(false)
            delete axios.defaults.headers.common['Authorization']
        }
    }

    const value = {
        isAuthenticated,
        user,
        loading,
        login,
        logout
    }

    return (
        <AuthContext.Provider value={value}>
            {children}
        </AuthContext.Provider>
    )
}
