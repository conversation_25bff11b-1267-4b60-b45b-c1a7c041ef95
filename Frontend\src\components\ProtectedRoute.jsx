import React from 'react'
import { Navigate } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'

const ProtectedRoute = ({ children }) => {
    const { isAuthenticated, loading } = useAuth()

    // Show loading while checking authentication
    if (loading) {
        return (
            <div className='min-vh-100 bg-primary d-flex justify-content-center align-items-center py-4'>
                <div className='container-fluid'>
                    <div className='row justify-content-center'>
                        <div className='col-lg-6 col-md-8 col-sm-10'>
                            <div className='bg-white p-4 rounded shadow text-center'>
                                <div className="spinner-border text-primary mb-3" role="status">
                                    <span className="visually-hidden">Loading...</span>
                                </div>
                                <h5 className='mb-2'>Verifying Access</h5>
                                <p className='text-muted'>Checking authentication status...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    }

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
        return <Navigate to="/login" replace />
    }

    // Render the protected component
    return children
}

export default ProtectedRoute
