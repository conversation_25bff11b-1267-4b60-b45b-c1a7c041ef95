import React, { useState } from 'react'
import {useNavigate, usePara<PERSON>, <PERSON>} from 'react-router-dom'
import {useEffect} from 'react'
import axios from 'axios'
import { useAuth } from './contexts/AuthContext'

function UpdateUser() {
    const [name, setName] = useState('')
    const [email, setEmail] = useState('')
    const [password,setPassword] = useState('')
    const [age, setAge] = useState('')
    const [technicalSkills, setTechnicalSkills] = useState([])
    const [gender, setGender] = useState('')
    const [image, setImage] = useState(null)
    const [currentImage, setCurrentImage] = useState('')
    const [imagePreview, setImagePreview] = useState(null)
    const navigate = useNavigate()
    const {id} = useParams()
    const { logout } = useAuth()

    // Handle image change
    const handleImageChange = (e) => {
        const file = e.target.files[0]
        if (file) {
            setImage(file)
            const reader = new FileReader()
            reader.onloadend = () => {
                setImagePreview(reader.result)
            }
            reader.readAsDataURL(file)
        }
    }

    // Handle technical skills checkbox change
    const handleTechnicalSkillChange = (skill) => {
        setTechnicalSkills(prev => {
            if (prev.includes(skill)) {
                return prev.filter(s => s !== skill)
            } else {
                return [...prev, skill]
            }
        })
    }

    // Load user data when component mounts
    useEffect(() => {
      axios.get(`http://localhost:4000/api/user/${id}`).then((response)=>{
        setName(response.data.name)
        setEmail(response.data.email)
        setPassword(response.data.password)
        setAge(response.data.age)
        setGender(response.data.gender)
        setTechnicalSkills(response.data.technicalSkills)
        setCurrentImage(response.data.image || '')
      })
      .catch((err)=>{
        console.log(err)
      })
    }, [id])

    // Handle form submission
    const handleSubmit = async (e) => {
      e.preventDefault()

      const formData = new FormData()
      formData.append('name', name)
      formData.append('email', email)
      formData.append('password', password)
      formData.append('age', age)
      formData.append('gender',gender)
      formData.append('technicalSkills', JSON.stringify(technicalSkills))
      if (image) {
          formData.append('image', image)
      }

      try {
          const response = await axios.put(`http://localhost:4000/api/update/${id}`, formData, {
              headers: {
                  'Content-Type': 'multipart/form-data'
              }
          })
          console.log(response.data)
          navigate('/')
      } catch (error) {
          if (error.response?.status === 401) {
              alert('Session expired. Please login again.')
              await logout()
              navigate('/login')
          } else {
              alert('Failed to update user. Please try again.')
              console.error(error)
          }
      }
    }
    


  return (
   <div className='min-vh-100 bg-primary d-flex justify-content-center align-items-start py-4'>
        <div className='container-fluid'>
            <div className='row justify-content-center'>
                <div className='col-lg-8 col-md-10 col-sm-12'>
                    <div className='bg-white p-4 rounded shadow'>
            <form onSubmit={handleSubmit}>
                <div className='d-flex justify-content-between align-items-center mb-3'>
                    <h2>Update User</h2>
                    <div>
                        <Link to='/' className='btn btn-outline-secondary btn-sm'>Back to Users</Link>
                    </div>
                </div>
                <div className='mb-2'>
                    <label>Name</label>
                    <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder='Enter Name'
                        className='form-control'
                    />
                </div>

                <div className='mb-2'>
                    <label>Email</label>
                    <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder='Enter Email'
                        className='form-control'
                    />
                </div>

                
                
                <div className='mb-2'>
                    <label >Password</label>
                    <input type="password" name="" value={password} 
                    onChange={(e)=>setPassword(e.target.value)} 
                    placeholder='Enter Email' className='form-control' />
                </div>

                <div className='mb-2'>
                    <label>Age</label>
                    <input
                        type="text"
                        value={age}
                        onChange={(e) => setAge(e.target.value)}
                        placeholder='Enter Age'
                        className='form-control'
                    />
                </div>


                <div className='mb-2'>
                    <label>Profile Image</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className='form-control'
                    />
                    {currentImage && !imagePreview && (
                        <div className='mt-2'>
                            <p>Current Image:</p>
                            <img
                                src={`http://localhost:4000/uploads/${currentImage}`}
                                alt="Current"
                                style={{width: '100px', height: '100px', objectFit: 'cover'}}
                            />
                        </div>
                    )}
                    {imagePreview && (
                        <div className='mt-2'>
                            <p>New Image Preview:</p>
                            <img
                                src={imagePreview}
                                alt="Preview"
                                style={{width: '100px', height: '100px', objectFit: 'cover'}}
                            />
                        </div>
                    )}
                </div>


                                <div className='mb-3'>
                    <label className='form-label'>Technical Skills</label>
                    <div className='row'>
                        <div className='col-md-6'>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='cpp'
                                    checked={technicalSkills.includes('C++')}
                                    onChange={() => handleTechnicalSkillChange('C++')}
                                />
                                <label className='form-check-label' htmlFor='cpp'>
                                    C++
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='python'
                                    checked={technicalSkills.includes('Python')}
                                    onChange={() => handleTechnicalSkillChange('Python')}
                                />
                                <label className='form-check-label' htmlFor='python'>
                                    Python
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='html'
                                    checked={technicalSkills.includes('HTML')}
                                    onChange={() => handleTechnicalSkillChange('HTML')}
                                />
                                <label className='form-check-label' htmlFor='html'>
                                    HTML
                                </label>
                            </div>
                        </div>
                        <div className='col-md-6'>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='css'
                                    checked={technicalSkills.includes('CSS')}
                                    onChange={() => handleTechnicalSkillChange('CSS')}
                                />
                                <label className='form-check-label' htmlFor='css'>
                                    CSS
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='js'
                                    checked={technicalSkills.includes('JavaScript')}
                                    onChange={() => handleTechnicalSkillChange('JavaScript')}
                                />
                                <label className='form-check-label' htmlFor='js'>
                                    JavaScript
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='dotnet'
                                    checked={technicalSkills.includes('.NET')}
                                    onChange={() => handleTechnicalSkillChange('.NET')}
                                />
                                <label className='form-check-label' htmlFor='dotnet'>
                                    .NET
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                    
                <div className='mb-3'>
                    <label className='form-label'>Gender</label>
                    <div>
                        <div className='form-check form-check-inline'>
                            <input
                                className='form-check-input'
                                type='radio'
                                name='gender'
                                id='male'
                                value='Male'
                                checked={gender === 'Male'}
                                onChange={(e) => setGender(e.target.value)}
                            />
                            <label className='form-check-label' htmlFor='male'>
                                Male
                            </label>
                        </div>
                        <div className='form-check form-check-inline'>
                            <input
                                className='form-check-input'
                                type='radio'
                                name='gender'
                                id='female'
                                value='Female'
                                checked={gender === 'Female'}
                                onChange={(e) => setGender(e.target.value)}
                            />
                            <label className='form-check-label' htmlFor='female'>
                                Female
                            </label>
                        </div>
                    </div>
                </div>
                        <button type="submit" className='btn btn-success'>Update</button>
                    </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
  )
}

export default UpdateUser