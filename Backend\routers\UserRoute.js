import express from 'express'
import UserModel from '../models/User.js'
import multer from 'multer'
import path from 'path'
import fs from 'fs'

// Create uploads directory if it doesn't exist
const uploadsDir = 'uploads'
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true })
}

const storage=multer.diskStorage({
    destination:(req,file,cb)=>{
        cb(null,'uploads/')
    },
    filename:(req,file,cb)=>{
        cb(null,Date.now()+path.extname(file.originalname))
    }
})

const upload = multer({
    storage: storage,
    fileFilter: (req, file, cb) => {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true)
        } else {
            cb(new Error('Only image files are allowed!'), false)
        }
    },
    limits: {
        fileSize: 5 * 1024 * 1024 // 5MB limit
    }
})

const router=express()


router.post('/create', upload.single('image'), (req,res)=>{
    const userData = {
        name: req.body.name,
        email: req.body.email,
        password:req.body.password,
        age: req.body.age,
        gender:req.body.gender,
        technicalSkills:req.body.technicalSkills,
        image: req.file ? req.file.filename : ''
    }

    UserModel.create(userData).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.get('/users',(req,res)=>{
    UserModel.find({}).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.get('/user/:id',(req,res)=>{
    UserModel.findById(req.params.id).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.status(400).json(err)
    })
})

router.put('/update/:id', upload.single('image'), (req,res)=>{
    const updateData = {
        name: req.body.name,
        email: req.body.email,
        password:req.body.password,
        age: req.body.age,
        gender:req.body.gender,
        technicalSkills:req.body.technicalSkills,

    }

    // Only update image if a new one is uploaded
    if (req.file) {
        updateData.image = req.file.filename
    }

    UserModel.findByIdAndUpdate(req.params.id, updateData, {new:true}).then((data)=>{
        res.json(data)
    })
    .catch((Err)=>{
        res.status(400).json(Err)
    })
})

router.delete('/delete/:id',(req,res)=>{
    UserModel.findByIdAndDelete(req.params.id).then((data)=>{
        res.json(data)
    })
    .catch((err)=>{
        res.json(err)
    })
})

export default router