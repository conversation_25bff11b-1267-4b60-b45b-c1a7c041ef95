import React from 'react'
import './App.css'
import {BrowserRouter,Route,Routes} from 'react-router-dom'
import 'bootstrap/dist/css/bootstrap.min.css'
import CreateUser from './CreateUser'
import UpdateUser from './UpdateUser'
import User from './User'
import Login from './Login'
import { AuthProvider } from './contexts/AuthContext'
import ProtectedRoute from './components/ProtectedRoute'

function App() {
  return (
    <>
      <BrowserRouter>
        <AuthProvider>
          <Routes>
            <Route path='/login' element={<Login/>}/>
            <Route path='/' element={
              <ProtectedRoute>
                <User/>
              </ProtectedRoute>
            }/>
            <Route path='/create' element={
              <ProtectedRoute>
                <CreateUser/>
              </ProtectedRoute>
            } />
            <Route path='/update/:id' element={
              <ProtectedRoute>
                <UpdateUser/>
              </ProtectedRoute>
            } />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </>
  )
}

export default App
