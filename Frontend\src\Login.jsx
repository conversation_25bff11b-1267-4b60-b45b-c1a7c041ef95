import React, { useState } from 'react'
import { useNavigate, Link } from 'react-router-dom'

function Login() {
    const [username, setUsername] = useState('')
    const [password, setPassword] = useState('')
    const [error, setError] = useState('')
    const [loading, setLoading] = useState(false)
    const navigate = useNavigate()

    // Hardcoded admin credentials
    const ADMIN_USERNAME = 'admin'
    const ADMIN_PASSWORD = 'admin123'

    const handleSubmit = (e) => {
        e.preventDefault()
        setError('')
        setLoading(true)

        // Simple validation with hardcoded credentials
        if (username === ADMIN_USERNAME && password === ADMIN_PASSWORD) {
            // Store login status in localStorage
            localStorage.setItem('isLoggedIn', 'true')
            localStorage.setItem('username', username)
            
            // Navigate to users list
            navigate('/')
        } else {
            setError('Invalid username or password')
        }
        
        setLoading(false)
    }

    const handleLogout = () => {
        localStorage.removeItem('isLoggedIn')
        localStorage.removeItem('username')
        navigate('/login')
    }

    // Check if already logged in
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true'

    if (isLoggedIn) {
        return (
            <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
                <div className='w-50 bg-white p-4 rounded text-center'>
                    <h2 className='mb-4'>Welcome, Admin!</h2>
                    <p className='mb-4'>You are already logged in.</p>
                    <div>
                        <Link to='/' className='btn btn-success me-3'>Go to Users</Link>
                        <button 
                            onClick={handleLogout}
                            className='btn btn-danger'
                        >
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className='d-flex vh-100 bg-primary justify-content-center align-items-center'>
            <div className='w-50 bg-white p-4 rounded'>
                <form onSubmit={handleSubmit}>
                    <h2 className='mb-4 text-center'>Admin Login</h2>
                    
                    {error && (
                        <div className='alert alert-danger' role='alert'>
                            {error}
                        </div>
                    )}

                    <div className='mb-3'>
                        <label className='form-label'>Username</label>
                        <input 
                            type="text" 
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            placeholder='Enter username' 
                            className='form-control'
                            required
                        />
                    </div>

                    <div className='mb-3'>
                        <label className='form-label'>Password</label>
                        <input 
                            type="password" 
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            placeholder='Enter password' 
                            className='form-control'
                            required
                        />
                    </div>

                    <button 
                        type="submit" 
                        className='btn btn-success w-100 mb-3'
                        disabled={loading}
                    >
                        {loading ? 'Logging in...' : 'Login'}
                    </button>

                    <div className='text-center'>
                        <div className='alert alert-info'>
                            <strong>Demo Credentials:</strong><br/>
                            Username: <code>admin</code><br/>
                            Password: <code>admin123</code>
                        </div>
                        <p>
                            <Link to='/'>View Users (Public)</Link>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    )
}

export default Login
