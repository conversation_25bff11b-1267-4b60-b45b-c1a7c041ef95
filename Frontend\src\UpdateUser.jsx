import React, { useState } from 'react'
import {useNavigate, usePara<PERSON>, <PERSON>} from 'react-router-dom'
import {useEffect} from 'react'
import axios from 'axios'
import { useAuth } from './contexts/AuthContext'

function UpdateUser() {
    const [name, setName] = useState('')
    const [email, setEmail] = useState('')
    const [password,setPassword] = useState('')
    const [age, setAge] = useState('')
    const [technicalSkills, setTechnicalSkills] = useState([])
    const [gender, setGender] = useState('')
    const [image, setImage] = useState(null)
    const [currentImage, setCurrentImage] = useState('')
    const [imagePreview, setImagePreview] = useState(null)
    const [loading, setLoading] = useState(true)
    const navigate = useNavigate()
    const {id} = useParams()
    const { logout } = useAuth()

    // Handle image change
    const handleImageChange = (e) => {
        const file = e.target.files[0]
        if (file) {
            setImage(file)
            const reader = new FileReader()
            reader.onloadend = () => {
                setImagePreview(reader.result)
            }
            reader.readAsDataURL(file)
        }
    }

    // Handle technical skills checkbox change
    const handleTechnicalSkillChange = (skill) => {
        setTechnicalSkills(prev => {
            if (prev.includes(skill)) {
                return prev.filter(s => s !== skill)
            } else {
                return [...prev, skill]
            }
        })
    }

    // Load user data when component mounts
    useEffect(() => {
      setLoading(true)
      axios.get(`http://localhost:4000/api/user/${id}`).then((response)=>{
        setName(response.data.name || '')
        setEmail(response.data.email || '')
        setPassword(response.data.password || '')
        setAge(response.data.age || '')
        setGender(response.data.gender || '')

        // Handle technical skills - ensure it's always an array
        let skills = response.data.technicalSkills || []

        if (typeof skills === 'string') {
          try {
            skills = JSON.parse(skills)
          } catch (error) {
            console.error('Error parsing technical skills:', error)
            skills = []
          }
        }
        if (!Array.isArray(skills)) {
          skills = []
        }

        console.log('=== UPDATE USER DEBUG ===')
        console.log('Full API response:', response.data)
        console.log('Raw technical skills:', response.data.technicalSkills)
        console.log('Processed skills array:', skills)
        console.log('=========================')
        setTechnicalSkills(skills)

        // TEMPORARY TEST: Force some skills to test checkboxes
        setTimeout(() => {
          console.log('Setting test skills...')
          setTechnicalSkills(['C++', 'Python', 'HTML'])
        }, 1000)

        setCurrentImage(response.data.image || '')
        setLoading(false)
      })
      .catch((err)=>{
        console.error('Error loading user data:', err)
        alert('Failed to load user data. Please try again.')
        setLoading(false)
      })
    }, [id])

    // Debug: Log technicalSkills state changes
    useEffect(() => {
        console.log('technicalSkills state changed:', technicalSkills)
    }, [technicalSkills])

    // Handle form submission
    const handleSubmit = async (e) => {
      e.preventDefault()

      const formData = new FormData()
      formData.append('name', name)
      formData.append('email', email)
      formData.append('password', password)
      formData.append('age', age)
      formData.append('gender',gender)
      formData.append('technicalSkills', JSON.stringify(technicalSkills))
      if (image) {
          formData.append('image', image)
      }

      try {
          const response = await axios.put(`http://localhost:4000/api/update/${id}`, formData, {
              headers: {
                  'Content-Type': 'multipart/form-data'
              }
          })
          console.log(response.data)
          navigate('/')
      } catch (error) {
          if (error.response?.status === 401) {
              alert('Session expired. Please login again.')
              await logout()
              navigate('/login')
          } else {
              alert('Failed to update user. Please try again.')
              console.error(error)
          }
      }
    }
    


  if (loading) {
    return (
      <div className='min-vh-100 bg-primary d-flex justify-content-center align-items-center py-4'>
        <div className='container-fluid'>
          <div className='row justify-content-center'>
            <div className='col-lg-6 col-md-8 col-sm-10'>
              <div className='bg-white p-4 rounded shadow text-center'>
                <div className="spinner-border text-primary mb-3" role="status">
                  <span className="visually-hidden">Loading...</span>
                </div>
                <h5 className='mb-2'>Loading User Data</h5>
                <p className='text-muted'>Please wait...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
   <div className='min-vh-100 bg-primary d-flex justify-content-center align-items-start py-4'>
        <div className='container-fluid'>
            <div className='row justify-content-center'>
                <div className='col-lg-8 col-md-10 col-sm-12'>
                    <div className='bg-white p-4 rounded shadow'>
            <form onSubmit={handleSubmit}>
                <div className='d-flex justify-content-between align-items-center mb-3'>
                    <h2>Update User</h2>
                    <div>
                        <Link to='/' className='btn btn-outline-secondary btn-sm'>Back to Users</Link>
                    </div>
                </div>
                <div className='mb-2'>
                    <label>Name</label>
                    <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder='Enter Name'
                        className='form-control'
                    />
                </div>

                <div className='mb-2'>
                    <label>Email</label>
                    <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder='Enter Email'
                        className='form-control'
                    />
                </div>

                
                
                <div className='mb-2'>
                    <label >Password</label>
                    <input type="password" name="" value={password} 
                    onChange={(e)=>setPassword(e.target.value)} 
                    placeholder='Enter Email' className='form-control' />
                </div>

                <div className='mb-2'>
                    <label>Age</label>
                    <input
                        type="text"
                        value={age}
                        onChange={(e) => setAge(e.target.value)}
                        placeholder='Enter Age'
                        className='form-control'
                    />
                </div>


                <div className='mb-2'>
                    <label>Profile Image</label>
                    <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className='form-control'
                    />
                    {currentImage && !imagePreview && (
                        <div className='mt-2'>
                            <p>Current Image:</p>
                            <img
                                src={`http://localhost:4000/uploads/${currentImage}`}
                                alt="Current"
                                style={{width: '100px', height: '100px', objectFit: 'cover'}}
                            />
                        </div>
                    )}
                    {imagePreview && (
                        <div className='mt-2'>
                            <p>New Image Preview:</p>
                            <img
                                src={imagePreview}
                                alt="Preview"
                                style={{width: '100px', height: '100px', objectFit: 'cover'}}
                            />
                        </div>
                    )}
                </div>


                                <div className='mb-3'>
                    <label className='form-label'>Technical Skills</label>
                    <div className='mb-2'>
                        <small className='text-muted'>
                            Debug: Current skills = [{technicalSkills.join(', ')}]
                        </small>
                    </div>
                    <div className='row'>
                        <div className='col-md-6'>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='update-cpp'
                                    checked={Array.isArray(technicalSkills) && technicalSkills.includes('C++')}
                                    onChange={() => handleTechnicalSkillChange('C++')}
                                />
                                <label className='form-check-label' htmlFor='update-cpp'>
                                    C++
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='update-python'
                                    checked={Array.isArray(technicalSkills) && technicalSkills.includes('Python')}
                                    onChange={() => handleTechnicalSkillChange('Python')}
                                />
                                <label className='form-check-label' htmlFor='update-python'>
                                    Python
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='update-html'
                                    checked={Array.isArray(technicalSkills) && technicalSkills.includes('HTML')}
                                    onChange={() => handleTechnicalSkillChange('HTML')}
                                />
                                <label className='form-check-label' htmlFor='update-html'>
                                    HTML
                                </label>
                            </div>
                        </div>
                        <div className='col-md-6'>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='update-css'
                                    checked={Array.isArray(technicalSkills) && technicalSkills.includes('CSS')}
                                    onChange={() => handleTechnicalSkillChange('CSS')}
                                />
                                <label className='form-check-label' htmlFor='update-css'>
                                    CSS
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='update-js'
                                    checked={Array.isArray(technicalSkills) && technicalSkills.includes('JavaScript')}
                                    onChange={() => handleTechnicalSkillChange('JavaScript')}
                                />
                                
                                <label className='form-check-label' htmlFor='update-js'>
                                    JavaScript
                                </label>
                            </div>
                            <div className='form-check'>
                                <input
                                    className='form-check-input'
                                    type='checkbox'
                                    id='update-dotnet'
                                    checked={Array.isArray(technicalSkills) && technicalSkills.includes('.NET')}
                                    onChange={() => handleTechnicalSkillChange('.NET')}
                                />
                                <label className='form-check-label' htmlFor='update-dotnet'>
                                    .NET
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                    
                <div className='mb-3'>
                    <label className='form-label'>Gender</label>
                    <div>
                        <div className='form-check form-check-inline'>
                            <input
                                className='form-check-input'
                                type='radio'
                                name='gender'
                                id='update-male'
                                value='Male'
                                checked={gender === 'Male'}
                                onChange={(e) => setGender(e.target.value)}
                            />
                            <label className='form-check-label' htmlFor='update-male'>
                                Male
                            </label>
                        </div>
                        <div className='form-check form-check-inline'>
                            <input
                                className='form-check-input'
                                type='radio'
                                name='gender'
                                id='update-female'
                                value='Female'
                                checked={gender === 'Female'}
                                onChange={(e) => setGender(e.target.value)}
                            />
                            <label className='form-check-label' htmlFor='update-female'>
                                Female
                            </label>
                        </div>
                    </div>
                </div>
                        <button type="submit" className='btn btn-success'>Update</button>
                    </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
  )
}

export default UpdateUser